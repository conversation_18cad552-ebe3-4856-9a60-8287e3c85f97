<template>
  <div>充电站监控</div>
  <el-card>
    <el-row :gutter="24">
      <el-col :span="6">
        <el-input v-model="formParams.search">
          <template #append>
            <el-select v-model="select" style="width: 115px">
              <el-option label="按名称查询" value="name"></el-option>
              <el-option label="按ID查询" value="id"></el-option>
            </el-select>
          </template>
        </el-input>

      </el-col>
      <el-col :span="6">
        <el-select v-model="formParams.status">
          <el-option label="全部" :value="1"></el-option>
          <el-option label="使用中" :value="2"></el-option>
          <el-option label="空闲中" :value="3"></el-option>
        </el-select>
      </el-col>
      <el-col :span="6" :offset="6">
        <el-button type="primary">查询</el-button>
        <el-button>重置</el-button>
      </el-col>
    </el-row>
  </el-card>

  <el-card class="mt">
    <el-row>
      <el-col :span="6">
        <el-statistic title="累计充电量(度)" :value="268900" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="累计充电次数(次)" :value="1389"></el-statistic>
      </el-col>
      <el-col :span="6">
        <el-statistic title="服务区域(个)" :value="88" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="累计效益(元)" :value="5622178"></el-statistic>
      </el-col>
    </el-row>
  </el-card>

  <el-card class="mt">
    <el-button type="primary" icon="Plus">新增充电站</el-button>
  </el-card>

  <el-card class="mt">
    <el-table :data="tableData">            
      <el-table-column type="index" label="序号" width="80"/>
      <el-table-column prop="name" label="站点名称" />
      <el-table-column prop="id" label="站点ID" />
      <el-table-column prop="city" label="所属城市" />
      <el-table-column prop="fast" label="快充数" />
      <el-table-column prop="slow" label="慢充数" />
      <el-table-column prop="status" label="充电站状态" />
      <el-table-column prop="now" label="正在充电" />
      <el-table-column prop="fault" label="充电故障" />
      <el-table-column prop="person" label="站点负责人" />
      <el-table-column prop="tel" label="负责人电话" />
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" >编辑</el-button>
          <el-button type="danger" >删除</el-button>
        </template>
      </el-table-column>

    </el-table>
  </el-card>
</template>

<script setup lang="ts">
import { ref } from "vue"
import {getTableData} from "@/api/chargingstation/tabledata"
const select = ref("name")
const formParams = ref({
  search: "",
  status: 1
})

const tableData=ref([])
const loadData= async ()=>{
  const  res=await getTableData()
}
</script>